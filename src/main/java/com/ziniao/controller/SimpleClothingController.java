package com.ziniao.controller;

import com.ziniao.model.ClothingRequest;
import com.ziniao.model.ClothingResponse;
import com.ziniao.model.FittingRoomRequest;
import com.ziniao.model.FittingRoomResponse;
import com.ziniao.model.DataWriteBackRequest;
import com.ziniao.model.DataWriteBackResponse;
import com.ziniao.service.ClothingService;
import com.ziniao.service.ResultService;
import com.ziniao.service.DataWriteBackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * AI穿衣控制器
 */
@Api(tags = "AI穿衣服务")
@RestController
@RequestMapping("/api/clothing")
@Validated
public class SimpleClothingController {
    
    private static final Logger logger = LoggerFactory.getLogger(SimpleClothingController.class);
    
    @Autowired
    private ClothingService clothingService;

    @Autowired
    private ResultService resultService;

    @Autowired
    private DataWriteBackService dataWriteBackService;
    
    /**
     * AI穿衣-上下装（官方文档格式）
     */
    @ApiOperation(value = "AI穿衣-上下装", notes = "完全按照官方文档格式的AI穿衣接口")
    @PostMapping("/fittingRoom")
    public ResponseEntity<FittingRoomResponse> fittingRoom(
            @ApiParam(value = "试衣间请求参数", required = true) @Valid @RequestBody FittingRoomRequest request) {

        try {
            logger.info("收到AI穿衣请求（官方格式）: {}", request);

            FittingRoomResponse response = clothingService.processFittingRoom(request);

            if (response.isSuccess()) {
                logger.info("AI穿衣处理成功（官方格式）");
                return ResponseEntity.ok(response);
            } else {
                logger.error("AI穿衣处理失败（官方格式）: {}", response.getMsg());
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            logger.error("AI穿衣处理异常（官方格式）", e);

            FittingRoomResponse errorResponse = new FittingRoomResponse();
            errorResponse.setCode("500");
            errorResponse.setMsg("服务器内部错误: " + e.getMessage());

            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * AI穿衣处理 - 上下装试穿（兼容接口）
     */
    @ApiOperation(value = "AI穿衣处理（兼容）", notes = "兼容性接口，建议使用官方格式接口")
    @PostMapping("/process")
    public ResponseEntity<ClothingResponse> processClothing(
            @ApiParam(value = "穿衣请求参数", required = true) @Valid @RequestBody ClothingRequest request) {
        
        try {
            logger.info("收到AI穿衣请求: {}", request);
            
            ClothingResponse response = clothingService.processClothing(request);
            
            if (response.isSuccess()) {
                logger.info("AI穿衣处理成功");
                return ResponseEntity.ok(response);
            } else {
                logger.error("AI穿衣处理失败: {}", response.getMessage());
                return ResponseEntity.badRequest().body(response);
            }
            
        } catch (Exception e) {
            logger.error("AI穿衣处理异常", e);
            
            ClothingResponse errorResponse = new ClothingResponse();
            errorResponse.setCode(500);
            errorResponse.setMessage("服务器内部错误: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
    
    /**
     * 查询AI穿衣任务结果
     */
    @ApiOperation(value = "查询AI穿衣结果", notes = "根据任务ID查询AI穿衣的处理结果和状态")
    @GetMapping("/result/{taskId}")
    public ResponseEntity<ClothingResponse> queryTaskResult(
            @ApiParam(value = "任务ID", required = true, example = "1945877291232894976")
            @PathVariable @NotBlank String taskId) {

        try {
            logger.info("查询任务结果, taskId: {}", taskId);

            ClothingResponse response = resultService.queryResult(taskId);

            if (response.isSuccess()) {
                logger.info("任务结果查询成功");
                return ResponseEntity.ok(response);
            } else {
                logger.error("任务结果查询失败: {}", response.getMessage());
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            logger.error("任务结果查询异常", e);

            ClothingResponse errorResponse = new ClothingResponse();
            errorResponse.setCode(500);
            errorResponse.setMessage("服务器内部错误: " + e.getMessage());

            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 查询AI穿衣任务状态（兼容性接口）
     */
    @ApiOperation(value = "查询任务状态", notes = "兼容性接口，建议使用查询结果接口")
    @GetMapping("/task/{taskId}")
    public ResponseEntity<ClothingResponse> queryTaskStatus(
            @ApiParam(value = "任务ID", required = true, example = "1945877291232894976")
            @PathVariable @NotBlank String taskId) {

        try {
            logger.info("查询任务状态, taskId: {}", taskId);

            ClothingResponse response = clothingService.queryTaskStatus(taskId);

            if (response.isSuccess()) {
                logger.info("任务状态查询成功");
                return ResponseEntity.ok(response);
            } else {
                logger.error("任务状态查询失败: {}", response.getMessage());
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            logger.error("任务状态查询异常", e);

            ClothingResponse errorResponse = new ClothingResponse();
            errorResponse.setCode(500);
            errorResponse.setMessage("服务器内部错误: " + e.getMessage());

            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
    
    /**
     * 数据回写接口
     */
    @ApiOperation(value = "数据回写", notes = "将AI处理结果或其他数据回写到多维表格指定行的字段中")
    @PostMapping("/writeback/{taskId}")
    public ResponseEntity<DataWriteBackResponse> writeBackData(
            @ApiParam(value = "任务ID", required = true, example = "1945877291232894976")
            @PathVariable @NotBlank String taskId,
            @ApiParam(value = "数据回写请求参数", required = true)
            @Valid @RequestBody DataWriteBackRequest request) {

        try {
            logger.info("收到数据回写请求: taskId={}, request={}", taskId, request);

            // 设置任务ID
            request.setTaskId(taskId);

            DataWriteBackResponse response = dataWriteBackService.writeBackData(request);

            if (response.isSuccess()) {
                logger.info("数据回写成功: taskId={}, recordId={}", taskId, request.getRecordId());
                return ResponseEntity.ok(response);
            } else {
                logger.error("数据回写失败: taskId={}, error={}", taskId, response.getMessage());
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            logger.error("数据回写异常: taskId={}", taskId, e);

            DataWriteBackResponse errorResponse = DataWriteBackResponse.error(500,
                    "服务器内部错误: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * AI穿衣服务健康检查
     */
    @ApiOperation(value = "健康检查", notes = "检查AI穿衣服务是否正常运行")
    @GetMapping("/health")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("AI穿衣服务运行正常");
    }
}
