package com.ziniao.service;

import com.ziniao.model.feishu.FeishuFieldMappingImageEnhancedRequest;
import com.ziniao.model.feishu.FeishuFieldMappingImageEnhancedResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 增强版字段映射图片上传服务测试
 */
@ExtendWith(MockitoExtension.class)
public class FeishuBitableServiceEnhancedTest {

    private static final Logger logger = LoggerFactory.getLogger(FeishuBitableServiceEnhancedTest.class);

    @InjectMocks
    private FeishuBitableService feishuBitableService;

    @Mock
    private ImageProxyService imageProxyService;

    private FeishuFieldMappingImageEnhancedRequest testRequest;

    @BeforeEach
    void setUp() {
        testRequest = new FeishuFieldMappingImageEnhancedRequest();
        testRequest.setAppToken("test_app_token");
        testRequest.setTableId("test_table_id");
        
        Map<String, String> fieldMapping = new HashMap<>();
        fieldMapping.put("👚上装正面图", "👚上装正面图url");
        fieldMapping.put("👚上装背面图", "👚上装背面图url");
        testRequest.setFieldMapping(fieldMapping);
        
        testRequest.setProcessId("test_process_001");
    }

    @Test
    void testValidateAndPreprocessEnhancedRequest_DefaultValues() {
        // 测试默认值设置
        testRequest.setPageSize(null);
        testRequest.setBatchSize(null);
        testRequest.setDownloadTimeout(null);
        testRequest.setMaxConcurrentDownloads(null);

        try {
            // 使用反射调用私有方法进行测试
            java.lang.reflect.Method method = FeishuBitableService.class
                    .getDeclaredMethod("validateAndPreprocessEnhancedRequest", FeishuFieldMappingImageEnhancedRequest.class);
            method.setAccessible(true);
            method.invoke(feishuBitableService, testRequest);

            // 验证默认值
            assertEquals(200, testRequest.getPageSize());
            assertEquals(50, testRequest.getBatchSize());
            assertEquals(30, testRequest.getDownloadTimeout());
            assertEquals(3, testRequest.getMaxConcurrentDownloads());

        } catch (Exception e) {
            logger.error("测试验证和预处理方法失败", e);
            fail("测试验证和预处理方法失败: " + e.getMessage());
        }
    }

    @Test
    void testValidateAndPreprocessEnhancedRequest_PageSizeLimit() {
        // 测试页面大小限制
        testRequest.setPageSize(600); // 超过500的限制

        try {
            java.lang.reflect.Method method = FeishuBitableService.class
                    .getDeclaredMethod("validateAndPreprocessEnhancedRequest", FeishuFieldMappingImageEnhancedRequest.class);
            method.setAccessible(true);
            method.invoke(feishuBitableService, testRequest);

            // 验证页面大小被限制为500
            assertEquals(500, testRequest.getPageSize());

        } catch (Exception e) {
            logger.error("测试页面大小限制失败", e);
            fail("测试页面大小限制失败: " + e.getMessage());
        }
    }

    @Test
    void testValidateAndPreprocessEnhancedRequest_EmptyFieldMapping() {
        // 测试空字段映射
        testRequest.setFieldMapping(new HashMap<>());

        try {
            java.lang.reflect.Method method = FeishuBitableService.class
                    .getDeclaredMethod("validateAndPreprocessEnhancedRequest", FeishuFieldMappingImageEnhancedRequest.class);
            method.setAccessible(true);

            assertThrows(java.lang.reflect.InvocationTargetException.class, () -> {
                method.invoke(feishuBitableService, testRequest);
            });

        } catch (Exception e) {
            logger.error("测试空字段映射失败", e);
            fail("测试空字段映射失败: " + e.getMessage());
        }
    }

    @Test
    void testHasSpecificTargets_WithRecordIds() {
        // 测试记录ID目标检测
        testRequest.setTargetRecordIds(Arrays.asList("rec001", "rec002"));

        try {
            java.lang.reflect.Method method = FeishuBitableService.class
                    .getDeclaredMethod("hasSpecificTargets", FeishuFieldMappingImageEnhancedRequest.class);
            method.setAccessible(true);
            boolean result = (boolean) method.invoke(feishuBitableService, testRequest);

            assertTrue(result);

        } catch (Exception e) {
            logger.error("测试记录ID目标检测失败", e);
            fail("测试记录ID目标检测失败: " + e.getMessage());
        }
    }

    @Test
    void testHasSpecificTargets_WithRowIndexes() {
        // 测试行索引目标检测
        testRequest.setTargetRowIndexes(Arrays.asList(1, 2, 3));

        try {
            java.lang.reflect.Method method = FeishuBitableService.class
                    .getDeclaredMethod("hasSpecificTargets", FeishuFieldMappingImageEnhancedRequest.class);
            method.setAccessible(true);
            boolean result = (boolean) method.invoke(feishuBitableService, testRequest);

            assertTrue(result);

        } catch (Exception e) {
            logger.error("测试行索引目标检测失败", e);
            fail("测试行索引目标检测失败: " + e.getMessage());
        }
    }

    @Test
    void testHasSpecificTargets_WithRowRange() {
        // 测试行范围目标检测
        Map<String, Integer> rowRange = new HashMap<>();
        rowRange.put("start", 1);
        rowRange.put("end", 10);
        testRequest.setRowRange(rowRange);

        try {
            java.lang.reflect.Method method = FeishuBitableService.class
                    .getDeclaredMethod("hasSpecificTargets", FeishuFieldMappingImageEnhancedRequest.class);
            method.setAccessible(true);
            boolean result = (boolean) method.invoke(feishuBitableService, testRequest);

            assertTrue(result);

        } catch (Exception e) {
            logger.error("测试行范围目标检测失败", e);
            fail("测试行范围目标检测失败: " + e.getMessage());
        }
    }

    @Test
    void testHasSpecificTargets_NoTargets() {
        // 测试无特定目标
        try {
            java.lang.reflect.Method method = FeishuBitableService.class
                    .getDeclaredMethod("hasSpecificTargets", FeishuFieldMappingImageEnhancedRequest.class);
            method.setAccessible(true);
            boolean result = (boolean) method.invoke(feishuBitableService, testRequest);

            assertFalse(result);

        } catch (Exception e) {
            logger.error("测试无特定目标失败", e);
            fail("测试无特定目标失败: " + e.getMessage());
        }
    }

    @Test
    void testGetMaxRowIndex() {
        // 测试获取最大行索引
        testRequest.setTargetRowIndexes(Arrays.asList(5, 10, 15));
        
        Map<String, Integer> rowRange = new HashMap<>();
        rowRange.put("start", 1);
        rowRange.put("end", 20);
        testRequest.setRowRange(rowRange);

        try {
            java.lang.reflect.Method method = FeishuBitableService.class
                    .getDeclaredMethod("getMaxRowIndex", FeishuFieldMappingImageEnhancedRequest.class);
            method.setAccessible(true);
            int result = (int) method.invoke(feishuBitableService, testRequest);

            assertEquals(20, result); // 应该返回范围的最大值

        } catch (Exception e) {
            logger.error("测试获取最大行索引失败", e);
            fail("测试获取最大行索引失败: " + e.getMessage());
        }
    }

    @Test
    void testConvertToStandardRequest() {
        // 测试转换为标准请求
        testRequest.setViewId("test_view_id");
        testRequest.setPageSize(300);
        testRequest.setDownloadTimeout(60);
        testRequest.setMaxConcurrentDownloads(5);
        testRequest.setUpdateBitableWithLocalUrl(true);
        testRequest.setIncludeImageDetails(false);

        try {
            java.lang.reflect.Method method = FeishuBitableService.class
                    .getDeclaredMethod("convertToStandardRequest", FeishuFieldMappingImageEnhancedRequest.class);
            method.setAccessible(true);
            Object result = method.invoke(feishuBitableService, testRequest);

            assertNotNull(result);
            // 这里可以进一步验证转换后的字段值

        } catch (Exception e) {
            logger.error("测试转换为标准请求失败", e);
            fail("测试转换为标准请求失败: " + e.getMessage());
        }
    }

    @Test
    void testAddErrorToResponse() {
        // 测试添加错误信息
        FeishuFieldMappingImageEnhancedResponse.Data responseData = 
                new FeishuFieldMappingImageEnhancedResponse.Data();

        try {
            java.lang.reflect.Method method = FeishuBitableService.class
                    .getDeclaredMethod("addErrorToResponse", 
                            FeishuFieldMappingImageEnhancedResponse.Data.class, 
                            String.class, String.class, String.class, String.class);
            method.setAccessible(true);
            method.invoke(feishuBitableService, responseData, "TEST_ERROR", "测试错误消息", "rec001", "testField");

            assertNotNull(responseData.getErrors());
            assertEquals(1, responseData.getErrors().size());
            
            FeishuFieldMappingImageEnhancedResponse.ErrorInfo error = responseData.getErrors().get(0);
            assertEquals("TEST_ERROR", error.getErrorType());
            assertEquals("测试错误消息", error.getErrorMessage());
            assertEquals("rec001", error.getRecordId());
            assertEquals("testField", error.getFieldName());
            assertNotNull(error.getTimestamp());

        } catch (Exception e) {
            logger.error("测试添加错误信息失败", e);
            fail("测试添加错误信息失败: " + e.getMessage());
        }
    }

    @Test
    void testCalculatePerformanceStats() {
        // 测试性能统计计算
        FeishuFieldMappingImageEnhancedResponse.Data responseData = 
                new FeishuFieldMappingImageEnhancedResponse.Data();
        responseData.setTotalRecords(100);
        responseData.setTotalImages(200);

        long startTime = System.currentTimeMillis() - 5000; // 5秒前

        try {
            java.lang.reflect.Method method = FeishuBitableService.class
                    .getDeclaredMethod("calculatePerformanceStats", 
                            FeishuFieldMappingImageEnhancedResponse.Data.class, long.class);
            method.setAccessible(true);
            method.invoke(feishuBitableService, responseData, startTime);

            assertTrue(responseData.getAvgRecordProcessingTime() > 0);
            assertTrue(responseData.getAvgImageDownloadTime() > 0);

        } catch (Exception e) {
            logger.error("测试性能统计计算失败", e);
            fail("测试性能统计计算失败: " + e.getMessage());
        }
    }

    /**
     * 集成测试：测试完整的增强版处理流程
     * 注意：这个测试需要真实的飞书环境，在单元测试中可能需要mock
     */
    @Test
    void testProcessFieldMappingImagesEnhanced_Integration() {
        // 这里可以添加集成测试逻辑
        // 由于需要真实的飞书API调用，建议在集成测试环境中运行
        logger.info("集成测试需要真实的飞书环境，跳过单元测试");
        assertTrue(true); // 占位符测试
    }
}
